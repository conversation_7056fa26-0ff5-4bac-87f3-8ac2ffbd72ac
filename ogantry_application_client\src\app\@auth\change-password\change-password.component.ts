import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { AuthService } from '@auth/auth.service';
import { AuthNoticeService } from '@auth/auth-notice/auth-notice.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { AppConstants } from '@shared/constants/app.constant';
import { AdministrationService } from '@entities/administration/administration.service';
import { ConfirmPasswordValidator } from '@auth/register/confirm-password.validator';
import { finalize, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'kt-change-password',
  templateUrl: './change-password.component.html',
  encapsulation: ViewEncapsulation.None
})
export class ChangePasswordComponent extends SflBaseComponent implements OnInit, OnDestroy {
  changePasswordForm: FormGroup;
  currentUserEmail: string;
  AppConstants = AppConstants;

  constructor(
    private readonly authService: AuthService,
    private readonly adminService: AdministrationService,
    public readonly authNoticeService: AuthNoticeService,
    private readonly translate: TranslateService,
    private readonly router: Router,
    private readonly fb: FormBuilder
  ) {
    super();
  }

  ngOnInit() {
    this.initChangePasswordForm();
    this.getCurrentUserEmail();
  }

  ngOnDestroy(): void {
    this.isSubmitting = false;
  }

  /**
   * Get current user email from auth service
   */
  private getCurrentUserEmail() {
    this.currentUserEmail = localStorage.getItem('userEmail');
    if (!this.currentUserEmail) {
      // If email not found in localStorage, try to get from auth service
      this.authService
        .getCurrentUser()
        .then((user) => {
          this.currentUserEmail = user?.email;
        })
        .catch(() => {
          this.authNoticeService.setNotice('Unable to get user information', 'danger');
        });
    }
  }

  /**
   * Form initialization with validators
   */
  initChangePasswordForm() {
    this.changePasswordForm = this.fb.group(
      {
        newPassword: ['', Validators.compose([Validators.required, Validators.minLength(AppConstants.requiredMinLength), Validators.maxLength(AppConstants.passwordMaxLength)])],
        confirmPassword: ['', Validators.compose([Validators.required, Validators.minLength(AppConstants.requiredMinLength), Validators.maxLength(AppConstants.passwordMaxLength)])]
      },
      {
        validators: [ConfirmPasswordValidator.MatchPassword]
      }
    );
  }

  /**
   * Form Submit
   */
  submit() {
    const controls = this.changePasswordForm.controls;

    // Check form validation
    if (this.changePasswordForm.invalid) {
      Object.keys(controls).forEach((controlName) => controls[controlName].markAsTouched());
      return;
    }

    if (!this.currentUserEmail) {
      this.authNoticeService.setNotice('User email not found. Please login again.', 'danger');
      return;
    }

    this.isSubmitting = true;

    const newPassword = controls.newPassword.value;
    const confirmPassword = controls.confirmPassword.value;

    this.adminService
      .updateUserPassword(this.currentUserEmail, newPassword, confirmPassword)
      .pipe(
        takeUntil(this.unsubscribe),
        finalize(() => {
          this.isSubmitting = false;
        })
      )
      .subscribe(
        (response) => {
          this.authNoticeService.setNotice('Password updated successfully', 'success');
          this.router.navigateByUrl(this.appRoutes.LOGIN);
        },
        (error) => {
          const errorMessage = error?.error?.message || 'Failed to update password. Please try again.';
          this.authNoticeService.setNotice(errorMessage, 'danger');
        }
      );
  }

  /**
   * Navigate back to login
   */
  goBack() {
    this.router.navigateByUrl(this.appRoutes.LOGIN);
  }
}
