<!--begin::User-->
<div ngbDropdown placement="top-right" display="dynamic" container="body" *ngIf="user$ | async as _user" class="dropdown user-profile-dropdown">
  <!--begin::Toggle-->
  <div ngbDropdownToggle class="topbar-item user-profile-toggle">
    <div class="d-flex align-items-center justify-content-between w-100">
      <div class="user-avatar-container">
        <img [src]="userAvatarUrl" [alt]="userDisplayName" class="user-avatar" />
        <div class="user-info d-none d-md-block ml-3">
          <div class="user-name">{{ userDisplayName }}</div>
          <div class="user-email">{{ userEmail }}</div>
        </div>
      </div>
      <span class="dropdown-arrow ml-2">
        <fa-icon [icon]="'chevron-down'"></fa-icon>
      </span>
    </div>
  </div>
  <!--end::Toggle-->

  <!--begin::Dropdown-->
  <div ngbDropdownMenu class="dropdown-menu user-profile-dropdown-menu">
    <!-- User Info Header -->
    <div class="user-profile-header">
      <!-- <div class="user-avatar-large">
        <img [src]="userAvatarUrl" [alt]="userDisplayName" class="avatar-img" />
      </div> -->
      <div class="user-details">
        <div class="user-name-large">{{ userDisplayName }}</div>
        <div class="user-email-small">{{ userEmail }}</div>
      </div>
    </div>

    <!-- Menu Items -->
    <div class="user-profile-menu d-flex flex-column">
      <a href="javascript:;" class="dropdown-item user-menu-item" (click)="resetPassword()">
        <img [src]="lockIcon" alt="Lock" class="menu-icon" />
        <span>{{ resetPasswordText }}</span>
        <fa-icon [icon]="'chevron-right'" class="arrow-icon"></fa-icon>
      </a>

      <a href="javascript:;" class="dropdown-item user-menu-item" (click)="openPrivacyPolicy()">
        <img [src]="privacyPolicyIcon" alt="Privacy Policy" class="menu-icon" />
        <span>{{ privacyPolicyText }}</span>
        <fa-icon [icon]="'chevron-right'" class="arrow-icon"></fa-icon>
      </a>

      <a href="javascript:;" class="dropdown-item user-menu-item logout-item" (click)="logout()">
        <img [src]="logOutIcon" alt="Log Out" />
        <span class="ml-3">{{ logOutText }}</span>
      </a>
    </div>

    <!-- Version Info -->
    <div class="version-info">{{ versionText }} {{ currentApplicationVersion }} : FE &nbsp; {{ backendVersion }} : BE</div>
  </div>
  <!--end::Dropdown-->
</div>
<!--end::User-->
