@import '/src/assets/sass/components/variables.demo';

// User Profile Dropdown Color Variables
$user-profile-header-bg: rgba(232, 232, 232, 1);
$user-profile-dropdown-bg: #ffffff;
$user-profile-dropdown-border: #e5e7eb;
$user-profile-dropdown-shadow: rgba(0, 0, 0, 0.1);

$user-profile-text-primary: rgba(75, 63, 114, 1);
$user-profile-text-secondary: #6b7280;
$user-profile-text-muted: #9ca3af;
$user-profile-text-white: rgba(63, 66, 84, 1);

$user-profile-text-white-muted: rgba(114, 114, 114, 1);

$user-profile-text-white-border: rgba(255, 255, 255, 0.3);

$user-profile-hover-bg: #f3f4f6;
$user-profile-hover-bg-white: rgba(255, 255, 255, 0.1);
$user-profile-version-bg: #f9fafb;

$user-profile-logout-color: #dc2626;
$user-profile-logout-hover-bg: #fef2f2;

$user-profile-divider-color: #020305;
$user-profile-divider-dark: #374151;

// Dark theme colors
$user-profile-dark-bg: #1f2937;
$user-profile-dark-item-bg: #374151;
$user-profile-dark-text: #d1d5db;
$user-profile-dark-text-muted: #9ca3af;
$user-profile-dark-border: #4b5563;

// Global dropdown overrides
::ng-deep .dropdown-menu.show {
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
}

// Ensure dropdown appears on top
::ng-deep .user-profile-dropdown {
  .dropdown-menu {
    z-index: 9999 !important;
  }
}

// User Profile Dropdown Styles
.user-profile-dropdown {
  position: relative !important;

  .user-profile-toggle {
    cursor: pointer;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: $user-profile-hover-bg-white;
    }

    .user-avatar-container {
      display: flex;
      align-items: center;
    }

    .user-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid $user-profile-text-white-border;
    }

    .user-info {
      .user-name {
        color: $user-profile-dropdown-bg;
        font-weight: 600;
        font-size: 0.875rem;
        line-height: 1.2;
        margin-bottom: 0.125rem;
      }

      .user-email {
        color: $user-profile-dropdown-bg;
        font-size: 0.75rem;
        line-height: 1.2;
      }
    }

    .dropdown-arrow {
      color: $user-profile-dropdown-bg;
      font-size: 0.75rem;
      transition: transform 0.2s ease;
    }

    &[aria-expanded='true'] .dropdown-arrow {
      transform: rotate(180deg);
    }
  }
}

// Dropdown Menu Styles
.user-profile-dropdown-menu {
  min-width: 220px;
  border: 1px solid $user-profile-dropdown-border;
  border-radius: 0.5rem;
  // box-shadow: 0 10px 25px $user-profile-dropdown-shadow;
  overflow: hidden;
  margin-bottom: 0.5rem !important;
  margin-top: 0 !important;
  z-index: 9999 !important;
  background-color: $user-profile-dropdown-bg;
  padding: 0;
  position: absolute !important;
  left: -30px !important;
  top: -188px !important;

  .user-profile-header {
    background: $user-profile-header-bg;
    padding: 1.25rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;

    .user-avatar-large {
      .avatar-img {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid $user-profile-text-white-border;
      }
    }

    .user-details {
      flex: 1;

      .user-name-large {
        color: $user-profile-text-white;
        font-weight: 600;
        font-size: 1rem;
        line-height: 1.4;
        margin-bottom: 0.125rem;
      }

      .user-email-small {
        color: $user-profile-text-white-muted;
        font-size: 0.8rem;
        line-height: 1.3;
      }
    }
  }

  .user-profile-menu {
    padding: 0.75rem 0;
    background-color: $user-profile-dropdown-bg;

    .user-menu-item {
      display: flex;
      align-items: center;
      padding: 0.75rem 1.5rem;
      color: $user-profile-text-white;
      transition: all 0.2s ease;
      border: none;
      background: none;
      width: 100%;
      text-align: left;

      &:hover {
        color: $user-profile-text-primary;
        //   text-decoration: none;
      }

      .menu-icon {
        width: 24px;
        height: 24px;
        margin-right: 0.875rem;
        transition: all 0.2s ease;

        // For SVG images - darker gray to match reference
        &[src] {
          filter: brightness(0) saturate(100%) invert(25%) sepia(15%) saturate(1230%) hue-rotate(202deg) brightness(95%) contrast(87%);
        }

        // For FontAwesome icons
        &:not([src]) {
          color: $user-profile-text-secondary;
        }
      }

      span {
        flex: 1;
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
      }

      .arrow-icon {
        width: 0.875rem;
        color: $user-profile-text-muted;
        transition: color 0.2s ease;
      }

      &:hover .menu-icon {
        // For SVG images - keep same color on hover
        &[src] {
          filter: brightness(0) saturate(100%) invert(25%) sepia(15%) saturate(1230%) hue-rotate(202deg) brightness(95%) contrast(87%);
        }

        // For FontAwesome icons
        &:not([src]) {
          color: $user-profile-text-secondary;
        }
      }

      &:hover .arrow-icon {
        color: $user-profile-text-secondary;
      }

      &.logout-item {
        color: $user-profile-logout-color;

        &:hover {
          background-color: $user-profile-logout-hover-bg;
          color: $user-profile-logout-color;
        }

        .logout-icon {
          color: $user-profile-logout-color;
        }

        &:hover .logout-icon {
          // For SVG images
          &[src] {
            filter: brightness(0) saturate(100%) invert(15%) sepia(100%) saturate(6279%) hue-rotate(3deg) brightness(99%) contrast(114%);
          }

          // For FontAwesome icons
          &:not([src]) {
            color: $user-profile-logout-color;
          }
        }
      }
    }

    .dropdown-divider {
      margin: 0.375rem 0;
      border-top: 1px solid $user-profile-divider-color;
    }
  }

  .version-info {
    padding: 0.625rem 1.5rem;
    background-color: $user-profile-header-bg;
    font-size: 0.7rem;
    text-align: center;
    border-top: 1px solid $user-profile-divider-color;
    font-weight: 400;
  }
} // End of ::ng-deep .dropdown-menu

// Responsive adjustments
@media (max-width: 768px) {
  .user-profile-dropdown {
    .user-profile-toggle {
      .user-info {
        display: none !important;
      }
    }
  }

  .user-profile-dropdown-menu {
    min-width: 220px;
    margin-right: 1rem;
  }
}

// Dark theme support (if needed)
// @media (prefers-color-scheme: dark) {
//   .user-profile-dropdown-menu {
//     background-color: $user-profile-dark-bg;

//     .user-profile-menu {
//       .user-menu-item {
//         color: $user-profile-dark-text;

//         // &:hover {
//         //   background-color: $user-profile-dark-item-bg;
//         //   color: $user-profile-text-white;
//         // }

//         .menu-icon,
//         .arrow-icon {
//           color: $user-profile-dark-text-muted;
//         }

//         &:hover .menu-icon,
//         &:hover .arrow-icon {
//           color: $user-profile-text-white;
//         }
//       }

//       .dropdown-divider {
//         border-top-color: $user-profile-divider-dark;
//       }
//     }

//     .version-info {
//       background-color: $user-profile-dark-item-bg;
//       color: $user-profile-dark-text-muted;
//       border-top-color: $user-profile-dark-border;
//     }
//   }
// }
