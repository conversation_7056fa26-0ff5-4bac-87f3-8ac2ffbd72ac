// Angular
import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
// RxJS
import { Observable } from 'rxjs/internal/Observable';
import { AuthService } from '@auth/auth.service';
import { APP_ROUTES } from '@shared/constants/routes.constant';
import { AppConstants } from '@shared/constants/app.constant';
import { ImageConstant } from '@shared/constants/imageconstant';
import { of, Subscription } from 'rxjs';
import { environment } from '@environment/environment';
const version = require('../../../../../../../package.json');
@Component({
  selector: 'kt-user-profile',
  templateUrl: './user-profile.component.html',
  styleUrls: ['./user-profile.component.scss']
})
export class UserProfileComponent implements OnInit {
  // Public properties
  user$: Observable<{ firstName: string; lastName: string }>;
  myProfile = APP_ROUTES.MY_PROFILE;
  @Input() userDropdownStyle = 'light';
  @Input() avatar = true;
  @Input() greeting = true;
  @Input() badge: boolean;
  @Input() icon: boolean;
  userEmail: string;
  currentApplicationVersion = version.version;
  productionFlag = environment.production;
  subscriptionManager: Subscription = new Subscription();
  backendVersion = '';
  currentRole = '';

  // New properties for image dropdown
  userAvatarUrl = ImageConstant.userCircle;
  logOutIcon = ImageConstant.logOut;
  userDisplayName = '';
  resetPasswordText = AppConstants.userProfileTexts.resetPassword;
  privacyPolicyText = AppConstants.userProfileTexts.privacyPolicy;
  logOutText = AppConstants.userProfileTexts.logOut;
  versionText = AppConstants.userProfileTexts.version;

  // Icon properties
  lockIcon = ImageConstant.lockSvg;
  privacyPolicyIcon = ImageConstant.privacyPolicy;

  constructor(private readonly authService: AuthService, private readonly router: Router) {}

  /**
   * On init
   */
  ngOnInit(): void {
    this.user$ = of({ lastName: 'User', firstName: 'Test' });
    this.userEmail = localStorage.getItem('userEmail');
    this.subscriptionManager.add(
      this.authService.getBackendBuildVersion().subscribe((res) => {
        this.backendVersion = res?.data?.release_version?.version;
      })
    );
    this.currentRole = localStorage.getItem('role').toUpperCase();
    this.setUserDisplayName();
  }

  /**
   * Set user display name from current role
   */
  private setUserDisplayName(): void {
    this.userDisplayName = this.currentRole || 'Admin';
  }

  /**
   * Log out
   */
  logout() {
    this.authService.logout();
  }

  /**
   * Open privacy policy
   */
  openPrivacyPolicy() {
    window.open('https://www.ogantry.com/privacyPolicy.html');
  }

  /**
   * Reset password functionality
   */
  resetPassword() {
    this.router.navigateByUrl(APP_ROUTES.RESET_PASSWORD);
  }
}
