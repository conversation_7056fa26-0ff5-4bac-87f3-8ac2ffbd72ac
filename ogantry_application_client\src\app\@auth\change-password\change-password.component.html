<!--begin::Body-->
<!-- <div class="d-flex flex-column-fluid flex-center mt-30 mt-lg-0 height-100"> -->
<!--begin::Change Password-->
<!-- <div class="login-form login-signin"> -->

<kt-auth-notice></kt-auth-notice>
<div class="card card-custom gutter-b" id="create_role_form">
  <app-card-header [cardTitle]="cardTitle" [cardSubTitle]="cardSubTitle"></app-card-header>
  <div class="card-body create-card">
    <form class="form" [formGroup]="changePasswordForm" autocomplete="off" *ngIf="changePasswordForm">
      <div class="form-group">
        <label class="form-label">New Password</label>
        <input type="password" class="form-control custom" placeholder="New Password" required formControlName="newPassword" autocomplete="new-password" sflPasswordEye />
        <small *ngIf="changePasswordForm && isControlHasError(changePasswordForm, 'newPassword', 'required')" class="form-text text-danger">New Password is required</small>
        <small *ngIf="changePasswordForm && isControlHasError(changePasswordForm, 'newPassword', 'minlength')" class="form-text text-danger"
          >Password must be at least 3 characters</small
        >
        <small *ngIf="changePasswordForm && isControlHasError(changePasswordForm, 'newPassword', 'maxlength')" class="form-text text-danger"
          >Password cannot exceed 100 characters</small
        >
      </div>

      <div class="form-group">
        <label class="form-label">Confirm Password</label>
        <input type="password" class="form-control custom" placeholder="Confirm Password" required formControlName="confirmPassword" autocomplete="new-password" sflPasswordEye />
        <small *ngIf="changePasswordForm && isControlHasError(changePasswordForm, 'confirmPassword', 'required')" class="form-text text-danger">Confirm Password is required</small>
        <small *ngIf="changePasswordForm && isControlHasError(changePasswordForm, 'confirmPassword', 'minlength')" class="form-text text-danger"
          >Password must be at least 3 characters</small
        >
        <small *ngIf="changePasswordForm && isControlHasError(changePasswordForm, 'confirmPassword', 'maxlength')" class="form-text text-danger"
          >Password cannot exceed 100 characters</small
        >
        <small *ngIf="changePasswordForm && isControlHasError(changePasswordForm, 'confirmPassword', 'ConfirmPassword')" class="form-text text-danger"
          >Passwords do not match</small
        >
      </div>

      <!--begin::Action-->
      <div class="form-group d-flex flex-wrap justify-content-end">
        <!-- <a [routerLink]="appRoutes.LOGIN" class="btn btn-light-primary font-weight-bold px-9 py-4 my-3 mx-4"> Cancel </a> -->
        <button (click)="submit()" class="btn btn-primary font-weight-bold px-9 py-4 my-3" [isSubmitting]="isSubmitting">Change Password</button>
      </div>
      <!--end::Action-->
    </form>
  </div>
  <!--end::Change Password-->
</div>
<!--end::Body-->
