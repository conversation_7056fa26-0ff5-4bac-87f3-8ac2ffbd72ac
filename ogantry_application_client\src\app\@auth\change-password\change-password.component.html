<!--begin::Body-->
<div class="d-flex flex-column-fluid flex-center mt-30 mt-lg-0 height-100">
  <!--begin::Change Password-->
  <div class="login-form login-signin">
    <div class="text-center mb-10 mb-lg-20">
      <h3 class="font-size-h1">Change Password</h3>
      <p class="text-muted font-weight-semi-bold">Enter your new password</p>
    </div>

    <kt-auth-notice></kt-auth-notice>

    <form class="form" [formGroup]="changePasswordForm" autocomplete="off">
      <div class="form-group">
        <mat-form-field>
          <mat-label>New Password</mat-label>
          <input matInput type="password" placeholder="New Password" formControlName="newPassword" autocomplete="new-password" sflPasswordEye />
          <mat-error *ngIf="isControlHasError(changePasswordForm, 'newPassword', 'required')">
            <strong>New Password is required</strong>
          </mat-error>
          <mat-error *ngIf="isControlHasError(changePasswordForm, 'newPassword', 'minlength')">
            <strong>Password must be at least 3 characters</strong>
          </mat-error>
          <mat-error *ngIf="isControlHasError(changePasswordForm, 'newPassword', 'maxlength')">
            <strong>Password cannot exceed 100 characters</strong>
          </mat-error>
        </mat-form-field>
      </div>

      <div class="form-group">
        <mat-form-field>
          <mat-label>Confirm Password</mat-label>
          <input matInput type="password" placeholder="Confirm Password" formControlName="confirmPassword" autocomplete="new-password" sflPasswordEye />
          <mat-error *ngIf="isControlHasError(changePasswordForm, 'confirmPassword', 'required')">
            <strong>Confirm Password is required</strong>
          </mat-error>
          <mat-error *ngIf="isControlHasError(changePasswordForm, 'confirmPassword', 'minlength')">
            <strong>Password must be at least 3 characters</strong>
          </mat-error>
          <mat-error *ngIf="isControlHasError(changePasswordForm, 'confirmPassword', 'maxlength')">
            <strong>Password cannot exceed 100 characters</strong>
          </mat-error>
          <mat-error *ngIf="isControlHasError(changePasswordForm, 'confirmPassword', 'ConfirmPassword')">
            <strong>Passwords do not match</strong>
          </mat-error>
        </mat-form-field>
      </div>

      <!--begin::Action-->
      <div class="form-group d-flex flex-wrap justify-content-end">
        <a [routerLink]="appRoutes.LOGIN" class="btn btn-light-primary font-weight-bold px-9 py-4 my-3 mx-4"> Cancel </a>
        <button (click)="submit()" class="btn btn-primary font-weight-bold px-9 py-4 my-3" [isSubmitting]="isSubmitting">Change Password</button>
      </div>
      <!--end::Action-->
    </form>
  </div>
  <!--end::Change Password-->
</div>
<!--end::Body-->
