import { NgModule } from '@angular/core';

import { AuthRoutingModule } from './auth-routing.module';
import { SharedModule } from '../@shared/shared.module';
import { LoginComponent } from './login/login.component';
import { RegisterComponent } from './register/register.component';
import { ForgotPasswordComponent } from './forgot-password/forgot-password.component';
import { ChangePasswordComponent } from './change-password/change-password.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { AuthNoticeService } from './auth-notice/auth-notice.service';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { AuthComponent } from './auth.component';
import { ReactiveFormsModule } from '@angular/forms';

@NgModule({
  declarations: [AuthComponent, LoginComponent, RegisterComponent, ForgotPasswordComponent, ChangePasswordComponent],
  imports: [SharedModule, AuthRoutingModule, MatFormFieldModule, MatInputModule, TranslateModule, MatCheckboxModule, ReactiveFormsModule, TranslateModule.forChild()],
  providers: [AuthNoticeService]
})
export class AuthModule {}
