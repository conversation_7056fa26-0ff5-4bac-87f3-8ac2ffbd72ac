import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { LoginComponent } from './login/login.component';
import { RegisterComponent } from './register/register.component';
import { ForgotPasswordComponent } from './forgot-password/forgot-password.component';
import { ChangePasswordComponent } from './change-password/change-password.component';
import { AuthComponent } from './auth.component';

const routes: Routes = [
  {
    path: '',
    component: AuthComponent,
    children: [
      {
        path: 'login',
        component: LoginComponent,
        data: {
          pageTitle: 'Login - SFL Base Project',
          pageDescription: 'SFL - Login with username and password to get access'
        }
      },
      {
        path: 'register',
        component: RegisterComponent,
        data: {
          pageTitle: 'Register - SFL Base Project',
          pageDescription: 'SFL - Register for new user'
        }
      },
      {
        path: 'forgot-password',
        component: ForgotPasswordComponent,
        data: {
          pageTitle: 'Forgot Password - SFL Base Project',
          pageDescription: 'SFL - Forgot password in case if you forgot your password you will get mail by entering email address'
        }
      },
      {
        path: 'reset-password',
        component: ChangePasswordComponent,
        data: {
          pageTitle: 'Change Password - SFL Base Project',
          pageDescription: 'SFL - Change your password'
        }
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AuthRoutingModule {}
