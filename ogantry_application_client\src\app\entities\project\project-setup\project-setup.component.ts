import { Component, EventEmitter, OnInit, Output, Input, OnDestroy, ChangeDetectionStrategy, ChangeDetectorRef, AfterViewInit, ViewChild, ElementRef } from '@angular/core';
import { OGantryHttpResponse } from '@shared/models';
import { Contact, Client } from '@entities/client/client.model';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ClientService } from '@entities/client/client.service';
import { ProjectService } from '../project.service';
import { BillingTypes, DailyExpenseType, Project, PositionExpenseList, AddExpense, MonthlyExpenseType, SelectedMonth } from '../project.model';
import { AuthNoticeService } from '@auth/auth-notice/auth-notice.service';
import moment from 'moment';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import { AlertType } from '@shared/models/alert-type.enum';
import { MessageService } from 'primeng/api';
import { forkJoin, merge, Observable, OperatorFunction, Subject, BehaviorSubject } from 'rxjs';
import { ContactPersonService } from '@shared/components/contact-person/contact-person.service';
import { Router } from '@angular/router';
import { LayoutConfigService } from '@shared/services/layout-config.service';
import { NgbAccordion, NgbTypeahead } from '@ng-bootstrap/ng-bootstrap';
import { debounceTime, distinctUntilChanged, filter, map } from 'rxjs/operators';
import { AuthService } from '@auth/auth.service';
import { AdministrationService } from '@entities/administration/administration.service';
import { ComponentsType } from '@shared/models/component-type-enum';
import { Data } from '../../utilization-management/utilization.model';
import { ExtendedFormComponent } from '@shared/components/extended-form/extended-form.component';
import { Debounce } from '@shared/decorators/debounce.decorator';
const expenses = [
  { label: 'Daily', value: 'daily' },
  { label: 'Monthly', value: 'monthly' }
];
enum Expenses {
  MONTHLY = 'monthly',
  DAILY = 'daily'
}
@Component({
  selector: 'app-project-setup',
  templateUrl: './project-setup.component.html',
  styleUrls: ['./project-setup.component.scss'],
  providers: [MessageService]
  // changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProjectSetupComponent extends SflBaseComponent implements OnInit, OnDestroy, AfterViewInit {
  dailyBillableHours;
  fixedCostCheck = true;
  fixedCost: any = null;
  fixedCostVisible = true;
  @ViewChild('customMonthCalender', { read: ElementRef }) p_calender: ElementRef<any>;
  selectedMonths: Date[] = [];
  selectedMonth: SelectedMonth[] = [];
  @Input() extendFieldsObj: any = {};
  @ViewChild('extendFrom') extendFiledComponent: ExtendedFormComponent;
  componentType = ComponentsType;
  extendFormVisible = false;
  copyOFCustomers = [];
  globalDetailId: any;
  @Output() sidebarOpen = new EventEmitter();
  @Output() positionSetupTab = new EventEmitter();
  @Output() secondStep = new EventEmitter();
  @Input() projectData: Project;
  @Input() isEdit: boolean;
  @Input() _tags: string[];
  billingType: any;
  bill: string;
  billingTypes = BillingTypes;
  projectSetupForm: FormGroup;
  customers = [];
  customerName: string;
  projectStatus: string;
  isSave = false;
  @Output() setProjectId = new EventEmitter();
  @Input() projectId: number;
  @Output() getProjectionData = new EventEmitter();
  @Output() overallProjectData = new EventEmitter();
  addExpenseForm: FormGroup;
  date: Date;
  expenseLoader = false;
  expenses = expenses;
  allowExpenseSelection = false;
  expenseEnum = Expenses;
  showDailyExpenseType = false;
  @Input() dailyExpenseTypes: DailyExpenseType[];
  @Input() monthlyExpenseTypes: MonthlyExpenseType[];
  projectExpenseList: PositionExpenseList[] = [];
  showCostError = false;
  editExpenseObject: AddExpense[] = [];
  cloneExpense: { [s: string]: PositionExpenseList } = {};
  showDescriptionError = false;
  @Output() callProjections = new EventEmitter();
  showDeleteDialog: boolean;
  deleteContactId: number;
  status = [];
  showExpenseDeleteDialog = false;
  deleteExpenseObj = null;
  @Output() clientSidebarOpen = new EventEmitter();
  @Output() extendSidebarOpen = new EventEmitter();
  newFlag = false;
  existingFlag = true;
  @ViewChild('acc') el: NgbAccordion;
  @Input() hasPositionFlag: boolean;
  showAddExpenseRowFlag = false;
  @Output() checkValidationStatus = new EventEmitter();
  @ViewChild('instance', { static: true }) instance: NgbTypeahead;
  focus$ = new Subject<string>();
  click$ = new Subject<string>();
  formatter = (client) => client.name;
  clientsProjects: Project[] = [];
  currentProjectName = '';
  needToValidateProjectName = false;
  extendFields: any;
  showRetainerPlugDialog = false;
  retainerPlugs = [];
  retainerPlugsLoading$ = new BehaviorSubject<boolean>(false);
  isRetainerPlugSubmitting = false;
  showRetainerPlugError = false;
  showAmountError = false;
  monthOptions = [];
  selectedRetainerMonths = [];
  retainerAmount: number;
  retainerReason: string;
  @Input() minDate: Date;
  @Input() maxDate: Date;
  isEditExpense = false;

  search: OperatorFunction<string, readonly Client[]> = (text$: Observable<string>) => {
    const debouncedText$ = text$.pipe(debounceTime(200), distinctUntilChanged());
    const clicksWithClosedPopup$ = this.click$.pipe(filter(() => !this.instance?.isPopupOpen()));
    const inputFocus$ = this.focus$;
    return merge(debouncedText$, inputFocus$, clicksWithClosedPopup$).pipe(
      map((term) => (term === '' ? this.customers : this.customers.filter((state) => new RegExp(`^${term}`, 'gmi').test(state.name))))
    );
  };
  constructor(
    private readonly layoutConfigService: LayoutConfigService,
    private readonly router: Router,
    private readonly cdf: ChangeDetectorRef,
    private readonly contactPersonService: ContactPersonService,
    private readonly layoutUtilsService: LayoutUtilsService,
    private readonly authNoticeService: AuthNoticeService,
    private readonly clientService: ClientService,
    private readonly projectService: ProjectService,
    private readonly authService: AuthService,
    private readonly adminService: AdministrationService
  ) {
    super();
  }

  async ngOnInit(): Promise<void> {
    this.getFixedCost();
    this.initForm();
    this.getProjectStatus();
    this.getBillingType();
    this.getClientList();
    this.listenChange();
    // this.getGlobalDetailsCategory();
  }

  panelChange(panel) {
    setTimeout(() => {
      if (document.getElementById(panel.panelId)) {
        document.getElementById(panel.panelId).scrollIntoView();
      }
    }, 2000);
  }

  ngAfterViewInit() {
    this.layoutConfigService.updateHeight$.next(true);
    this.cdf.detectChanges();
    setTimeout(() => this.highlightSelectedMonths(), 100);
  }

  getProjectStatus() {
    this.subscriptionManager.add(
      this.projectService.getProjectStatus().subscribe((res) => {
        const projectStatuses = res?.data?.project_statuses || [];
        this.status = projectStatuses.map((status) => status.project_status.name);
      })
    );
  }

  ngOnDestroy() {
    this.authNoticeService.setNotice(null);
  }

  getProjectExpenses() {
    forkJoin([this.projectService.getProjectDailyExpenses(this.projectId), this.projectService.getProjectMonthlyExpenses(this.projectId)]).subscribe((res) => {
      res[0]?.data?.project_daily_expenses?.forEach((expense) => {
        this.projectExpenseList.push({ ...expense.project_daily_expense, key: 'Daily' });
      });
      res[1]?.data?.project_monthly_expenses?.forEach((expense) => {
        this.projectExpenseList.push({ ...expense.project_monthly_expense, key: 'Monthly' });
      });
      this.cdf.detectChanges();
    });
  }

  async getProjectData(): Promise<void> {
    this.loading$.next(true);
    this.setProjectForm();
    this.loading$.next(false);
    this.cdf.detectChanges();
  }

  getProject() {
    this.projectService.getProject(this.projectId).subscribe((res) => {
      this._tags = res.data?.project?.tags;
      this.cdf.detectChanges();
    });
  }

  getClientTags() {
    this.clientService.getClient(this.projectSetupForm.get('customer_id').value.id).subscribe((res) => {
      this._tags = res.data?.customer?.tags;
    });
  }

  setProjectForm() {
    const project = this.projectData?.project;
    this.projectSetupForm.controls['billing_type'].setValue(project.billing_type);
    this.projectSetupForm.controls['amount'].setValue(project.amount);
    this.projectSetupForm.controls['name'].setValue(project.name);
    this.projectSetupForm.controls['description'].setValue(project.description);
    this.projectSetupForm.controls['customer_id'].setValue(project.customer);
    this.customerName = project.customer.name;
    this.projectSetupForm.controls['status'].setValue(project.status);
    this.projectStatus = project.status;
    this.currentProjectName = this.projectSetupForm.controls['name'].value;
    if (this.isEdit) {
      this.getProjectExpenses();
      this.isSave = true;
    }
    this.cdf.detectChanges();
  }

  initForm() {
    this.projectSetupForm = new FormGroup({
      customer_id: new FormControl('', Validators.required),
      name: new FormControl('', Validators.required),
      description: new FormControl(''),
      billing_type: new FormControl('', Validators.required),
      amount: new FormControl(null),
      region_id: new FormControl(''),
      status: new FormControl('Draft'),
      clientFlag: new FormControl('exist')
    });

    this.projectStatus = 'Draft';

    this.projectSetupForm.valueChanges.subscribe((res) => {
      this.isSave = false;
      this.cdf.detectChanges();
    });

    // if user changes the name of the project then we would need to validate whether that name is available or not.
    this.projectSetupForm.get('name').valueChanges.subscribe((projectNameChanges) => {
      this.needToValidateProjectName = true;
    });

    this.addExpenseForm = new FormGroup({
      cost: new FormControl('', Validators.required),
      description: new FormControl('', Validators.required),
      date: new FormControl(this.date),
      month: new FormControl(''),
      year: new FormControl(''),
      type_id: new FormControl('', Validators.required),
      type: new FormControl('')
    });
  }

  onAmountChange() {
    this.isSave = false;
  }

  getClientList() {
    this.subscriptionManager.add(
      this.clientService.getClientData({ order_by: 'asc:name' }).subscribe((res) => {
        res?.body?.data?.customers?.map((cust) => {
          this.customers.push({
            name: cust.customer.name,
            id: cust.customer.id,
            is_active: cust?.customer?.is_active ? true : false
          });
        });
        this.copyOFCustomers = JSON.parse(JSON.stringify(this.customers));
        this.customers = this.customers?.filter((cust) => cust?.is_active === true);
        this.cdf.detectChanges();
      })
    );
  }

  openSidebar(contactPerson?: Contact, isEdit = false): void {
    const data = {
      project: this.projectData,
      isEdit: isEdit,
      selectedContactPerson: contactPerson,
      isAddContactVisible: true
    };
    this.sidebarOpen.emit(data);
    this.cdf.detectChanges();
  }

  showModal(contactPerson) {
    this.showDeleteDialog = true;
    this.deleteContactId = contactPerson.contact.id;
  }

  deleteContact() {
    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.contactPersonService.deleteProjectContactPerson(this.deleteContactId).subscribe(
        () => {
          this.isSubmitting = false;
          this.closeModal();
          this.layoutUtilsService.showActionNotification('Contact Person has been deleted successsfully', AlertType.Success);
          this.getProjectData();
        },
        () => (this.isSubmitting = true)
      )
    );
  }

  closeModal() {
    this.deleteContactId = null;
    this.showDeleteDialog = false;
  }

  billRateChange(value, flag = false) {
    if (flag && value !== 'Time and Materials') {
      this.projectSetupForm.controls['amount'].setValidators(Validators.required);
      this.projectSetupForm.get('amount').updateValueAndValidity();
    } else {
      this.projectSetupForm.controls['amount'].setValidators([]);
      this.projectSetupForm.get('amount').updateValueAndValidity();
    }
    this.cdf.detectChanges();
  }

  getBillingType() {
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.projectService.getBillingType().subscribe((res) => {
        this.billingType = res;
        this.bill = res.billing_types;
        this.loading$.next(false);
        this.cdf.detectChanges();
      })
    );
  }

  setCustomerId(event) {
    this.projectSetupForm.controls.customer_id.setValue({ name: event.customer.name, id: event.customer.id });
    this.cdf.detectChanges();
  }

  setProjectStatus(status) {
    this.projectStatus = status;
    this.projectSetupForm.controls.status.setValue(status);
    this.cdf.detectChanges();
  }

  detectChanges() {
    this.cdf.detectChanges();
  }

  createProjectSetup() {
    if (!this.checkFormForValidation(this.projectSetupForm)) {
      this.isSubmitting = true;
      const projectSetupForm = JSON.parse(JSON.stringify(this.projectSetupForm.value)); // generating deep copy of our form value
      projectSetupForm.region_id = 1;
      projectSetupForm.customer_id = Number(projectSetupForm.customer_id.id);
      delete projectSetupForm.clientFlag;
      let service = '';
      let successMsg = '';
      if (this.projectId) {
        service = 'editProjectSetup';
        successMsg = 'Project updated successfully';
        projectSetupForm.id = this.projectId;
      } else {
        service = 'createProjectSetup';
        successMsg = 'Project created successfully';
      }

      if (projectSetupForm.billing_type === this.billingTypes.TIME_MATERIALS) {
        projectSetupForm.amount = null;
      }

      const customerProjectDetailsRequest = {
        customer_name: this.copyOFCustomers?.filter((cust) => cust.id === projectSetupForm.customer_id)[0]?.name
      };
      // will need to make an api call to validate if there is any same project name exist under the selected client.
      // making this api call each time user get to hit the create project, as there is a chance that another user has created the project in the mean time.
      this.subscriptionManager.add(
        this.projectService.getProjectDataWithClientName(customerProjectDetailsRequest).subscribe(async (res) => {
          if (res.body.data.projects) this.clientsProjects = res.body.data.projects;
          // now we will need to check if there is any same named project exist within this clientprojects, if so we won't let end user create the same again.
          if (await this.checkIfSameProjectExist(projectSetupForm?.name)) {
            this.layoutUtilsService.showActionNotification('A project with the same name already exists for this client.', AlertType.Error);
            this.isSubmitting = false;
            this.cdf.detectChanges();
            return;
          }
          projectSetupForm.name = projectSetupForm.name.trim();
          let finalProjectObj;
          if (this.extendFiledComponent?.extendFieldsObj) {
            finalProjectObj = { ...projectSetupForm };
            finalProjectObj['extended_fields'] = this.extendFiledComponent?.extendFieldsObj;
          } else {
            finalProjectObj = {
              ...projectSetupForm
            };
          }
          this.subscriptionManager.add(
            this.projectService[service]({ ...finalProjectObj, days_billable: this.fixedCostVisible ? this.fixedCost : null }, this.projectId).subscribe(
              (res) => {
                this.setProjectId.emit(res.data.project.id);
                this.onSuccess(res.data, successMsg);
                if (!this.projectId) {
                  this.getProjectionData.emit(res.data.project.id);
                }
                this.extendFieldsObj = res?.data?.project?.extended_fields || {};
                this.cdf.detectChanges();
                this.overallProjectData.emit(res.data);
                this.checkValidationStatus.emit();
                this.currentProjectName = res.data.project.name;
                this.needToValidateProjectName = false;
                this.isSave = true;
                this.el.activeIds = [...this.el.activeIds, ...['custom-panel-3']];
                this.layoutConfigService.updateHeight$.next(true);
                this.cdf.detectChanges();
                this.layoutConfigService.updateHeight$.next(true);
                this.cdf.detectChanges();
                this.getClientTags();
              },
              (err) => this.onError(err)
            )
          );
        })
      );
      // if (this.extendFields) {
      //   this.updateExtendedFiled(this.extededdata());
      // }
    }
  }

  checkIfSameProjectExist(projectName: string): Promise<boolean> {
    return new Promise((resolve) => {
      if (this.needToValidateProjectName && this.projectSetupForm.controls['name'].value !== this.currentProjectName) {
        const existingProject = this.clientsProjects.filter((cProject) => cProject.project?.name?.toLowerCase()?.trim() === projectName?.toLowerCase()?.trim());
        resolve(existingProject?.length > 0);
      } else {
        resolve(false);
      }
    });
  }

  onSuccess(project: Project, successMsg: string) {
    this.isSubmitting = false;
    this.projectData = project;
    this.authNoticeService.setNotice(successMsg, 'success');
  }

  onError(err) {
    this.isSubmitting = false;
    const error: OGantryHttpResponse<Project> = err.error;
    this.authNoticeService.setNotice(error.errors, 'danger');
  }

  expenseSelected(value) {
    this.allowExpenseSelection = true;
    if (value.value === this.expenseEnum.DAILY) {
      this.showDailyExpenseType = true;
      this.addExpenseForm.controls['date'].setValidators([]);
      this.addExpenseForm.get('date').updateValueAndValidity();
    } else {
      this.showDailyExpenseType = false;
      // this.addExpenseForm.controls['date'].setValidators(Validators.required);
      this.addExpenseForm.get('type_id').updateValueAndValidity();
    }
  }

  resetFilter() {
    this.showDailyExpenseType = false;
    this.allowExpenseSelection = false;
    this.addExpenseForm.reset();
    this.showAddExpenseRowFlag = false;
    this.resetExpenseMonthValue();
  }

  getMonthName(monNumber) {
    return moment(monNumber, 'MM').format('MMM');
  }

  editExpense(expense, index) {
    this.isEditExpense = true;
    this.editExpenseObject[index] = {
      id: expense.id,
      cost: expense.cost,
      description: expense.description,
      type_id: expense?.key === 'Daily' ? expense?.type?.id : expense?.expense_type?.id,
      date: new Date(expense?.year, expense?.month - 1),
      type: expense.key
    };
    this.cloneExpense[expense.id] = JSON.parse(JSON.stringify(expense));
  }
  saveEditExpense(index) {
    if (this.editExpenseObject[index].cost && this.editExpenseObject[index].description) {
      const expenseData = JSON.parse(JSON.stringify(this.projectExpenseList));
      this.expenseLoader = true;
      this.projectExpenseList = [];
      let saveEditExpense = {};
      let editExpenseService = '';
      if (this.editExpenseObject[index].type === 'Daily') {
        saveEditExpense = {
          cost: this.editExpenseObject[index].cost,
          description: this.editExpenseObject[index].description,
          type_id: this.editExpenseObject[index].type_id,
          project_id: this.projectId
        };
        editExpenseService = 'updateProjectDailyExpense';
      } else {
        saveEditExpense = {
          cost: this.editExpenseObject[index].cost,
          description: this.editExpenseObject[index].description,
          month: this.editExpenseObject[index].date ? new Date(this.editExpenseObject[index].date).getMonth() + 1 : '',
          year: this.editExpenseObject[index].date ? new Date(this.editExpenseObject[index].date).getFullYear() : '',
          type_id: this.editExpenseObject[index].type_id,
          project_id: this.projectId
        };
        editExpenseService = 'updateProjectMonthlyExpense';
      }

      this.subscriptionManager.add(
        this.projectService[editExpenseService](this.editExpenseObject[index].id, saveEditExpense).subscri(
          (res) => {
            this.expenseLoader = false;
            this.isEditExpense = false;
            this.projectExpenseList = expenseData;
            if (res?.data?.project_daily_expense) {
              this.projectExpenseList[index] = { ...res?.data?.project_daily_expense, key: 'Daily' };
            } else if (res?.data?.project_monthly_expense) {
              this.projectExpenseList[index] = { ...res?.data?.project_monthly_expense, key: 'Monthly' };
            }
            this.checkValidationStatus.emit();
            this.cdf.detectChanges();
            this.layoutUtilsService.showActionNotification('Expenses updated successfully', AlertType.Success);
          },
          () => {
            this.expenseLoader = false;
          }
        )
      );
    } else {
      if (!this.editExpenseObject[index].cost) {
        this.showCostError = true;
      } else {
        this.showDescriptionError = true;
      }
    }
  }

  cancelEditExpense(expense, index) {
    this.isEditExpense = false;
    this.showCostError = false;
    this.showDescriptionError = false;
    this.projectExpenseList[index] = this.cloneExpense[expense.id];
    delete this.cloneExpense[expense.id];
  }

  expenseCostChange(index) {
    this.showCostError = !this.editExpenseObject[index].cost;
  }

  expenseDescChange(index) {
    this.showDescriptionError = !this.editExpenseObject[index].description;
  }

  addExpense() {
    if (!this.checkFormForValidation(this.addExpenseForm)) {
      const addExpenseForm = this.addExpenseForm.value;
      if (addExpenseForm.type === this.expenseEnum.MONTHLY && !this.selectedMonths?.length) {
        return;
      }
      this.expenseLoader = true;
      const expenseList = this.projectExpenseList;
      this.projectExpenseList = [];
      let addExpenseServcie = '';
      let addExpenseObj = {};
      if (addExpenseForm.type === this.expenseEnum.DAILY) {
        addExpenseObj = {
          cost: addExpenseForm.cost,
          description: addExpenseForm.description,
          project_id: Number(this.projectId),
          type_id: addExpenseForm.type_id
        };
        addExpenseServcie = 'addProjectDailyExpense';
      } else {
        if (this.selectedMonth && this.selectedMonths?.length) {
          addExpenseServcie = 'addProjectMonthlyExpense';
          const addExpenseObjectAPIcall = this.readyMultipleAPIforAddExpense(addExpenseForm, addExpenseServcie);
          this.addMultipleExpense(addExpenseObjectAPIcall, expenseList);
        }
      }
      if (addExpenseForm.type === this.expenseEnum.DAILY) {
        this.subscriptionManager.add(
          this.projectService[addExpenseServcie](addExpenseObj).subscribe(
            (res) => {
              this.expenseLoader = false;
              this.projectExpenseList = expenseList;
              if (res?.data?.project_daily_expense) {
                this.projectExpenseList.push({ ...res?.data?.project_daily_expense, key: 'Daily' });
              }
              this.onMonthlyExpenseAddedSuccessFully();
            },
            () => {
              this.expenseLoader = false;
              this.projectExpenseList = expenseList;
              this.cdf.detectChanges();
            }
          )
        );
      }
    }
  }

  confirmDeleteExpense(expense) {
    this.showExpenseDeleteDialog = true;
    this.deleteExpenseObj = expense;
  }

  deleteExpense() {
    this.isSubmitting = true;
    let deleteExpenseService = '';
    if (this.deleteExpenseObj.key === 'Daily') {
      deleteExpenseService = 'deleteProjectDailyExpense';
    } else {
      deleteExpenseService = 'deleteProjectMonthlyExpense';
    }

    this.subscriptionManager.add(
      this.projectService[deleteExpenseService](this.deleteExpenseObj.id).subscribe(
        (res) => {
          this.projectExpenseList = this.projectExpenseList.filter((expense) => expense.id !== this.deleteExpenseObj.id);
          this.isSubmitting = false;
          this.closeDeleteModal();
          this.cdf.detectChanges();
          this.layoutUtilsService.showActionNotification('Expense deleted successfully', AlertType.Success);
        },
        () => {
          this.isSubmitting = false;
          this.cdf.detectChanges();
        }
      )
    );
  }

  closeDeleteModal() {
    this.deleteExpenseObj = null;
    this.showExpenseDeleteDialog = false;
  }

  clientValue(event) {
    if (event.target.id === 'new') {
      this.clientSidebarOpen.emit();
    }
  }
  gotoNextTab() {
    this.positionSetupTab.emit();
  }
  setClientFlag() {
    this.newFlag = false;
    this.existingFlag = false;
    this.projectSetupForm.controls['clientFlag'].setValue('exist');
    this.cdf.detectChanges();
  }
  showAddExpenseRow() {
    this.showAddExpenseRowFlag = true;
  }

  getGlobalDetailsCategory(): void {
    this.subscriptionManager.add(
      this.adminService.getExtendedFields('ManageExtendedFiled').subscribe(
        (res) => {
          this.loading$.next(true);
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'ManageExtendedFiled') {
              // this.extendFields = res.data.global_details[0].global_detail.extended_fields.extendArray || [];
              this.extendFields = res.data.global_details[0].global_detail.extended_fields.extendArray || [];
              this.globalDetailId = res.data.global_details[0].global_detail.id;
              this.loading$.next(false);
            }
          }
        },
        () => this.loading$.next(false)
      )
    );
  }

  updateValue(index: number, index2: number, index3: number, event: any) {
    if (!this.extendFields[index].jsonData.extendedFieldsConfig[index2].fields[index3].data) {
      this.extendFields[index].jsonData.extendedFieldsConfig[index2].fields[index3].data = [];
      this.extendFields[index].jsonData.extendedFieldsConfig[index2].fields[index3].data.push({ id: this.projectId, value: event.target.value });
    } else {
      const findIndex = this.extendFields[index].jsonData.extendedFieldsConfig[index2].fields[index3].data.findIndex((item) => item.id === this.projectId);
      if (findIndex === -1) {
        this.extendFields[index].jsonData.extendedFieldsConfig[index2].fields[index3].data.push({ id: this.projectId as number, value: event.target.value });
      } else {
        this.extendFields[index].jsonData.extendedFieldsConfig[index2].fields[index3].data[findIndex].value = event.target.value;
      }
    }
  }
  searchValue(index: number, index2: number, index3: number): string {
    if (this.extendFields[index].jsonData.extendedFieldsConfig[index2].fields[index3].data) {
      const findIndex = this.extendFields[index].jsonData.extendedFieldsConfig[index2].fields[index3].data.findIndex((item) => item.id === this.projectId);
      if (findIndex !== -1 && this.extendFields[index].jsonData.extendedFieldsConfig[index2].fields[index3].data[findIndex].value) {
        return this.extendFields[index].jsonData.extendedFieldsConfig[index2].fields[index3].data[findIndex].value || '';
      }
    }
    return '';
  }

  updateExtendedFiled(processData: any) {
    this.subscriptionManager.add(
      this.adminService.updateTag(processData, this.adminService.extended_Filed.id).subscribe((res) => {
        this.isSubmitting = false;
        if (this.adminService.extended_Filed.id) {
          // this.onSuccess(res.data, 'Extended-Filed updated successfully');
          this.extendSidebarOpen.emit(false);
        } else {
          // this.onSuccess(res.data, 'Extended-Filed created successfully');
        }
      })
    );
  }

  extededdata(): any {
    let extededDataPrePare = {
      name: 'ManageExtendedFiled',
      extended_fields: {}
    };
    if (this.adminService.extended_Filed.id) {
      extededDataPrePare = this.adminService.extended_Filed;
    }
    extededDataPrePare.extended_fields = { extendArray: this.extendFields };
    return extededDataPrePare;
  }

  openExtendFormVisible(): void {
    this.extendSidebarOpen.emit(true);
  }

  openRetainerPlugDialog(): void {
    if (!this.projectId) {
      this.layoutUtilsService.showActionNotification(this.appConstants.saveProjectFirstMessage, AlertType.Warning);
      return;
    }

    this.retainerPlugsLoading$.next(true);
    this.showRetainerPlugDialog = true;
    this.retainerAmount = null;
    this.getRetainerPlugs();
  }

  closeRetainerPlugDialog(): void {
    this.showRetainerPlugDialog = false;
    this.showRetainerPlugError = false;
    this.showAmountError = false;
    this.selectedRetainerMonths = [];
    this.retainerAmount = null;
    this.retainerReason = '';
  }

  onMonthSelectionChange(): void {
    this.showRetainerPlugError = false;
    this.showAmountError = false;
  }

  valueChange(key: string): void {
    if (key === 'amount') {
      this.showAmountError = false;
    }
    if (key === 'reason') {
      this.showRetainerPlugError = false;
    }
  }

  getRetainerPlugs(): void {
    const params = { project_id: this.projectId };
    this.subscriptionManager.add(
      this.projectService.getRetainerPlugs(params).subscribe(
        (res) => {
          if (res?.data?.retainer_plugs) {
            this.retainerPlugs = res.data.retainer_plugs;
            this.monthOptions = this.retainerPlugs.map((plug) => {
              return {
                value: plug.retainer_plug.id,
                label: `${this.getMonthName(plug.retainer_plug.month)} ${plug.retainer_plug.year}`,
                data: plug.retainer_plug,
                sortValue: plug.retainer_plug.year * 100 + plug.retainer_plug.month
              };
            });

            this.monthOptions.sort((a, b) => b.sortValue - a.sortValue);
            this.selectedRetainerMonths = [];
            this.retainerAmount = null;
            this.retainerReason = '';
          } else {
            this.retainerPlugs = [];
            this.monthOptions = [];
          }
          this.retainerPlugsLoading$.next(false);
          this.cdf.detectChanges();
        },
        () => {
          this.retainerPlugsLoading$.next(false);
          this.cdf.detectChanges();
        }
      )
    );
  }

  saveRetainerPlugs(): void {
    if (this.selectedRetainerMonths?.length === 0) {
      return;
    }

    let isValid = true;

    if (!this.retainerAmount && this.retainerAmount !== 0) {
      this.showAmountError = true;
      isValid = false;
    }

    if (!this.retainerReason) {
      this.showRetainerPlugError = true;
      isValid = false;
    }

    if (!isValid) {
      return;
    }

    this.showRetainerPlugError = false;
    this.showAmountError = false;
    this.isRetainerPlugSubmitting = true;

    const updateObservables = this.selectedRetainerMonths.map((month) => {
      const plugData = {
        id: month.value,
        amount: this.retainerAmount,
        reason: this.retainerReason,
        month: month.data.month,
        year: month.data.year,
        project_id: this.projectId
      };

      return this.projectService.updateRetainerPlugs(plugData);
    });

    this.subscriptionManager.add(
      forkJoin(updateObservables).subscribe(
        (next) => {
          this.layoutUtilsService.showActionNotification(
            `${this.appConstants.retainerPlugSuccessMessage} ${this.selectedRetainerMonths.length} ${this.appConstants.retainerPlugSuccessMessageSuffix}`,
            AlertType.Success
          );
          this.isRetainerPlugSubmitting = false;
          this.showRetainerPlugDialog = false;
          this.checkValidationStatus.emit();
          this.cdf.detectChanges();
        },
        (err) => {
          this.layoutUtilsService.showActionNotification(err?.error?.message || this.appConstants.retainerPlugErrorMessage, AlertType.Error);
          this.isRetainerPlugSubmitting = false;
          this.cdf.detectChanges();
        }
      )
    );
  }

  extendFieldsDataChange(): void {
    this.isSave = false;
  }

  addMultipleExpense(apiCalls: any[], expenseList: any[]): void {
    this.subscriptionManager.add(
      forkJoin(apiCalls).subscribe({
        next: (responses: any[]) => {
          const allSuccess = responses?.every((res) => res?.success);
          if (allSuccess) {
            this.projectExpenseList = expenseList;
            const extractedExpenses = responses.map((res) => res?.data?.project_monthly_expense).filter((expense) => expense);
            extractedExpenses?.forEach((expense) => {
              this.projectExpenseList.push({
                ...expense,
                key: 'Monthly'
              });
            });
            this.onMonthlyExpenseAddedSuccessFully();
          }
        },
        error: () => {
          this.expenseLoader = false;
          this.projectExpenseList = expenseList;
          this.resetExpenseMonthValue();
          this.cdf.detectChanges();
        }
      })
    );
  }

  readyMultipleAPIforAddExpense(addExpenseForm: any, addExpenseService: string): Array<any> {
    if (!this.selectedMonth?.length) {
      return [];
    }

    return this.selectedMonth.map(({ month, year }) => {
      const addExpenseObj = {
        cost: addExpenseForm.cost,
        description: addExpenseForm.description,
        project_id: Number(this.projectId),
        type_id: addExpenseForm.type_id,
        month,
        year
      };

      return this.projectService[addExpenseService](addExpenseObj);
    });
  }

  onMonthlyExpenseAddedSuccessFully(): void {
    this.addExpenseForm.reset();
    this.expenseLoader = false;
    this.showDailyExpenseType = false;
    this.allowExpenseSelection = false;
    this.showAddExpenseRowFlag = false;
    this.callProjections.emit();
    this.checkValidationStatus.emit();
    this.resetExpenseMonthValue();
    this.cdf.detectChanges();
    this.layoutUtilsService.showActionNotification(this.appConstants.expenseAddedSuccess, AlertType.Success);
  }

  resetExpenseMonthValue(): void {
    this.selectedMonths = [];
    this.selectedMonth = [];
  }

  onMonthSelect(): void {
    const uniqueMonths: Date[] = [];
    const uniqueMonthsMap = new Map<string, boolean>();
    this.selectedMonths?.forEach((date) => {
      const month = new Date(date).getMonth() + 1;
      const year = new Date(date).getFullYear();
      const key = `${month}-${year}`;
      if (!uniqueMonthsMap.has(key)) {
        uniqueMonthsMap.set(key, true);
        uniqueMonths.push(date);
      }
    });

    if (uniqueMonths.length < (this.selectedMonths?.length || 0)) {
      this.selectedMonths = uniqueMonths;
    }

    this.selectedMonth = this.selectedMonths?.map((date) => ({
      month: new Date(date).getMonth() + 1,
      year: new Date(date).getFullYear()
    }));

    this.cdf.detectChanges();
  }

  highlightSelectedMonths(event?: any): void {
    const monthButtons = document.querySelectorAll('.p-monthpicker-month');
    const calendarTitle = document.querySelector('.p-datepicker-year')?.textContent;

    if (!calendarTitle) return;

    const yearMatch = calendarTitle.match(this.appConstants.MonthCalenderConfig.calenderTitleMatch);
    if (!yearMatch) return;

    const visibleYear = parseInt(yearMatch[0]);

    monthButtons?.forEach((el, index) => {
      el.classList.remove('selected-month-yellow', 'disabled-month');

      this.selectedMonths?.forEach((date) => {
        if (date.getFullYear() === visibleYear && date.getMonth() === index) {
          el.classList.add('selected-month-yellow');
        }
      });

      const monthDate = new Date(visibleYear, index, 1);
      if (this.minDate && this.maxDate) {
        if (monthDate < new Date(this.minDate.getFullYear(), this.minDate.getMonth(), 1) || monthDate > new Date(this.maxDate.getFullYear(), this.maxDate.getMonth(), 1)) {
          el.classList.add('disabled-month');
        }
      }
    });
  }

  listenChange(): void {
    const nextButton = document.querySelector('.p-datepicker-next');
    if (nextButton) {
      nextButton.addEventListener('click', () => {
        setTimeout(() => {
          this.highlightSelectedMonths();
        }, 0);
      });
    }
  }

  getFormattedMonthYear(date: Date): string {
    const monthNames = this.appConstants.MonthCalenderConfig.monthName;
    const month = new Date(date).getMonth();
    const year = new Date(date).getFullYear().toString().slice(-2);
    return `${monthNames[month]}/${year}`;
  }

  removeSelectedMonth(dateToRemove: Date): void {
    const monthToRemove = new Date(dateToRemove).getMonth();
    const yearToRemove = new Date(dateToRemove).getFullYear();

    this.selectedMonths = this.selectedMonths.filter((date) => {
      const month = new Date(date).getMonth();
      const year = new Date(date).getFullYear();
      return !(month === monthToRemove && year === yearToRemove);
    });

    this.onMonthSelect();
  }

  @Debounce(200)
  hideCalendarNavButtons(): void {
    const popups = document.querySelectorAll('.p-datepicker');
    popups.forEach((popup) => {
      if (popup && popup.parentElement && popup.parentElement.contains(this.p_calender.nativeElement)) {
        const nextBtn = popup.querySelector('.p-datepicker-next') as HTMLElement;
        const prevBtn = popup.querySelector('.p-datepicker-prev') as HTMLElement;
        if (nextBtn) nextBtn.style.display = 'none';
        if (prevBtn) prevBtn.style.display = 'none';
      }
    });
  }

  nextStep(): void {
    this.secondStep.emit();
  }

  getFixedCost(): void {
    this.subscriptionManager.add(
      this.adminService.getFixedCostDetails('fixed cost').subscribe((res) => {
        this.fixedCost = res?.data?.global_details?.[0]?.global_detail?.extended_fields.days_billable || null;
        this.fixedCostVisible = this.fixedCost ? true : false;
      })
    );
  }

  updateCheck(): void {
    if (this.isEdit) {
      if (this.projectData?.project?.days_billable !== null && this.projectData.project?.days_billable !== this.fixedCost) {
        this.isSave = false;
      }
      this.fixedCostVisible = this.projectData.project?.days_billable ? true : false;
    }
  }
}
